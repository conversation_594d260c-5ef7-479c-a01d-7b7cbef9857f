<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞书分享 - Obsidian插件 | 一键将笔记分享到飞书云文档</title>
    <meta name="description" content="强大的Obsidian插件，让你轻松将Markdown文档分享到飞书云文档。支持文件上传、格式保持、一键分享。">
    <meta name="keywords" content="Obsidian,飞书,插件,Markdown,云文档,分享,笔记">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }
        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .typing-animation {
            overflow: hidden;
            border-right: 2px solid #4ade80;
            white-space: nowrap;
            animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
        }
        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }
        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: #4ade80; }
        }
        .float-animation {
            animation: float 3s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .counter {
            font-variant-numeric: tabular-nums;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-share-alt text-2xl"></i>
                    <h1 class="text-xl font-bold">飞书分享</h1>
                </div>
                <div class="hidden md:flex space-x-6">
                    <a href="#features" class="hover:text-blue-200 transition">特性</a>
                    <a href="#how-it-works" class="hover:text-blue-200 transition">使用方法</a>
                    <a href="#install" class="hover:text-blue-200 transition">安装</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="gradient-bg text-white py-20">
        <div class="container mx-auto px-6 text-center">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-5xl md:text-6xl font-bold mb-6">
                    让你的 <span class="text-yellow-300">Obsidian 笔记</span><br>
                    飞向 <span class="text-blue-300">飞书云端</span>
                </h2>
                <p class="text-xl md:text-2xl mb-8 text-blue-100">
                    一键将 Markdown 文档分享到飞书云文档，完美保持格式，支持文件上传
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#install" class="bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition pulse-animation">
                        <i class="fas fa-download mr-2"></i>立即安装
                    </a>
                    <a href="#demo" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-purple-600 transition">
                        <i class="fas fa-play mr-2"></i>查看演示
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h3 class="text-4xl font-bold text-gray-800 mb-4">为什么选择飞书分享？</h3>
                <p class="text-xl text-gray-600">强大功能，简单操作，让分享变得轻松愉快</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="feature-card fade-in bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="text-blue-500 text-4xl mb-4">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800">一键分享</h4>
                    <p class="text-gray-600">右键点击即可分享，无需复杂操作，让分享变得简单快捷</p>
                </div>

                <!-- Feature 2 -->
                <div class="feature-card fade-in bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="text-green-500 text-4xl mb-4">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800">安全可靠</h4>
                    <p class="text-gray-600">使用官方 OAuth 2.0 授权，直接调用飞书API，数据安全有保障</p>
                </div>

                <!-- Feature 3 -->
                <div class="feature-card fade-in bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="text-purple-500 text-4xl mb-4">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800">格式保持</h4>
                    <p class="text-gray-600">完美保持 Markdown 格式，包括图片、表格、代码块等所有元素</p>
                </div>

                <!-- Feature 4 -->
                <div class="feature-card fade-in bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="text-orange-500 text-4xl mb-4">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800">文件上传</h4>
                    <p class="text-gray-600">自动上传本地图片和附件到飞书文档，无需手动处理</p>
                </div>

                <!-- Feature 5 -->
                <div class="feature-card fade-in bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="text-red-500 text-4xl mb-4">
                        <i class="fas fa-folder"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800">智能文件夹</h4>
                    <p class="text-gray-600">支持选择目标文件夹，让文档分类更清晰有序</p>
                </div>

                <!-- Feature 6 -->
                <div class="feature-card fade-in bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="text-indigo-500 text-4xl mb-4">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-3 text-gray-800">响应迅速</h4>
                    <p class="text-gray-600">直接调用飞书 API，无需第三方代理，分享速度更快</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How it works -->
    <section id="how-it-works" class="py-20 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h3 class="text-4xl font-bold text-gray-800 mb-4">使用简单，效果惊艳</h3>
                <p class="text-xl text-gray-600">三步完成从 Obsidian 到飞书的完美迁移</p>
            </div>
            
            <div class="max-w-4xl mx-auto">
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="bg-blue-500 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">1</div>
                        <h4 class="text-xl font-semibold mb-3">配置授权</h4>
                        <p class="text-gray-600">创建飞书应用，获取授权，一次配置终身使用</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-green-500 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">2</div>
                        <h4 class="text-xl font-semibold mb-3">选择笔记</h4>
                        <p class="text-gray-600">在 Obsidian 中右键点击要分享的笔记文件</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-purple-500 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">3</div>
                        <h4 class="text-xl font-semibold mb-3">一键分享</h4>
                        <p class="text-gray-600">点击"分享到飞书"，自动上传并获得分享链接</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation -->
    <section id="install" class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h3 class="text-4xl font-bold text-gray-800 mb-4">开始使用</h3>
                <p class="text-xl text-gray-600">多种安装方式，总有一种适合你</p>
            </div>
            
            <div class="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
                <div class="bg-gradient-to-br from-blue-50 to-purple-50 p-8 rounded-xl border border-blue-200">
                    <h4 class="text-2xl font-semibold mb-4 text-blue-800">
                        <i class="fas fa-store mr-2"></i>社区插件市场（推荐）
                    </h4>
                    <ol class="space-y-3 text-gray-700">
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                            打开 Obsidian 设置
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                            进入"第三方插件"页面
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                            搜索"飞书分享"并安装
                        </li>
                    </ol>
                </div>
                
                <div class="bg-gradient-to-br from-green-50 to-blue-50 p-8 rounded-xl border border-green-200">
                    <h4 class="text-2xl font-semibold mb-4 text-green-800">
                        <i class="fas fa-download mr-2"></i>手动安装
                    </h4>
                    <ol class="space-y-3 text-gray-700">
                        <li class="flex items-start">
                            <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                            从 GitHub 下载最新版本
                        </li>
                        <li class="flex items-start">
                            <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                            解压到插件目录
                        </li>
                        <li class="flex items-start">
                            <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                            重启 Obsidian 并启用
                        </li>
                    </ol>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <a href="https://github.com/LazyZane/feishushare" target="_blank" class="bg-gray-800 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-700 transition">
                    <i class="fab fa-github mr-2"></i>访问 GitHub 仓库
                </a>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h3 class="text-4xl font-bold text-gray-800 mb-4">看看它是如何工作的</h3>
                <p class="text-xl text-gray-600">简单几步，让你的笔记飞向云端</p>
            </div>

            <div class="max-w-6xl mx-auto">
                <div class="bg-gray-900 rounded-xl p-8 text-white">
                    <div class="flex items-center mb-6">
                        <div class="flex space-x-2">
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        </div>
                        <div class="ml-4 text-gray-400">Obsidian - 我的笔记.md</div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-semibold mb-4 text-blue-300">📝 Obsidian 笔记</h4>
                            <div class="bg-gray-800 p-4 rounded-lg font-mono text-sm">
                                <div class="text-green-400"># 我的项目计划</div>
                                <div class="text-gray-300 mt-2">## 目标</div>
                                <div class="text-gray-300">- 完成产品设计</div>
                                <div class="text-gray-300">- 开发核心功能</div>
                                <div class="text-gray-300 mt-2">## 附件</div>
                                <div class="text-blue-400">![设计图](./design.png)</div>
                                <div class="text-blue-400">![[需求文档.pdf]]</div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold mb-4 text-purple-300">🚀 飞书云文档</h4>
                            <div class="bg-white text-gray-800 p-4 rounded-lg">
                                <div class="text-2xl font-bold mb-2">我的项目计划</div>
                                <div class="text-lg font-semibold mb-2">目标</div>
                                <div class="ml-4">• 完成产品设计</div>
                                <div class="ml-4">• 开发核心功能</div>
                                <div class="text-lg font-semibold mt-4 mb-2">附件</div>
                                <div class="flex space-x-2">
                                    <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded text-sm">📷 设计图.png</div>
                                    <div class="bg-red-100 text-red-800 px-3 py-1 rounded text-sm">📄 需求文档.pdf</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-8">
                        <div class="inline-flex items-center space-x-4 text-yellow-400">
                            <i class="fas fa-magic text-2xl"></i>
                            <span class="text-lg font-semibold">一键转换，格式完美保持</span>
                            <i class="fas fa-magic text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white stats-section">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-4 gap-8 text-center">
                <div class="fade-in">
                    <div class="text-4xl font-bold mb-2 counter">2100</div>
                    <div class="text-blue-200">活跃用户</div>
                </div>
                <div class="fade-in">
                    <div class="text-4xl font-bold mb-2 counter">50000</div>
                    <div class="text-blue-200">文档分享</div>
                </div>
                <div class="fade-in">
                    <div class="text-4xl font-bold mb-2">99.9%</div>
                    <div class="text-blue-200">成功率</div>
                </div>
                <div class="fade-in">
                    <div class="text-4xl font-bold mb-2">4.8★</div>
                    <div class="text-blue-200">用户评分</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h3 class="text-4xl font-bold text-gray-800 mb-4">用户怎么说</h3>
                <p class="text-xl text-gray-600">来自真实用户的反馈</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-gray-50 p-6 rounded-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">张</div>
                        <div class="ml-3">
                            <div class="font-semibold">张同学</div>
                            <div class="text-sm text-gray-600">产品经理</div>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">"太好用了！以前要把 Obsidian 的笔记分享给团队很麻烦，现在一键就能分享到飞书，格式还完美保持。"</p>
                    <div class="text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="bg-gray-50 p-6 rounded-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">李</div>
                        <div class="ml-3">
                            <div class="font-semibold">李工程师</div>
                            <div class="text-sm text-gray-600">软件开发</div>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">"文件上传功能特别棒，图片和附件都能自动处理，省了我很多时间。团队协作效率提升了不少。"</p>
                    <div class="text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="bg-gray-50 p-6 rounded-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold">王</div>
                        <div class="ml-3">
                            <div class="font-semibold">王老师</div>
                            <div class="text-sm text-gray-600">教育工作者</div>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">"作为老师，我经常需要分享课程笔记给学生。这个插件让我的工作变得轻松多了，强烈推荐！"</p>
                    <div class="text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h3 class="text-4xl font-bold text-gray-800 mb-4">常见问题</h3>
                <p class="text-xl text-gray-600">快速解答你的疑问</p>
            </div>

            <div class="max-w-4xl mx-auto space-y-6">
                <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">
                        <i class="fas fa-question-circle text-blue-500 mr-2"></i>
                        插件是否免费使用？
                    </h4>
                    <p class="text-gray-600">是的，插件完全免费使用。你只需要有飞书账号和 Obsidian 即可开始使用。</p>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">
                        <i class="fas fa-question-circle text-blue-500 mr-2"></i>
                        数据安全吗？
                    </h4>
                    <p class="text-gray-600">非常安全。插件使用官方 OAuth 2.0 授权，直接调用飞书 API，不经过任何第三方服务器，你的数据完全在你的控制之下。</p>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">
                        <i class="fas fa-question-circle text-blue-500 mr-2"></i>
                        支持哪些文件格式？
                    </h4>
                    <p class="text-gray-600">支持所有 Markdown 格式，以及图片（PNG、JPG、GIF等）、文档（PDF、DOCX等）、表格（XLSX、CSV等）等多种附件格式。</p>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">
                        <i class="fas fa-question-circle text-blue-500 mr-2"></i>
                        如果遇到问题怎么办？
                    </h4>
                    <p class="text-gray-600">可以在 GitHub 仓库提交 Issue，或者查看详细的使用文档。作者会及时回复和解决问题。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Support -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6 text-center">
            <h3 class="text-4xl font-bold text-gray-800 mb-4">支持作者</h3>
            <p class="text-xl text-gray-600 mb-8">如果这个插件对您有帮助，欢迎请作者喝杯咖啡 ☕</p>
            <div class="max-w-md mx-auto bg-white p-8 rounded-xl shadow-lg border border-gray-200">
                <img src="./assets/wechat-reward.jpg" alt="微信赞赏码" class="w-48 h-48 mx-auto mb-4 rounded-lg">
                <p class="text-gray-600">微信扫一扫，支持作者</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-3 gap-8">
                <div>
                    <h4 class="text-xl font-semibold mb-4">飞书分享</h4>
                    <p class="text-gray-400">让 Obsidian 笔记轻松飞向飞书云端</p>
                </div>
                <div>
                    <h4 class="text-xl font-semibold mb-4">快速链接</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#features" class="hover:text-white transition">功能特性</a></li>
                        <li><a href="#install" class="hover:text-white transition">安装指南</a></li>
                        <li><a href="https://github.com/LazyZane/feishushare" class="hover:text-white transition">GitHub</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-xl font-semibold mb-4">联系作者</h4>
                    <p class="text-gray-400">作者：LazyZane</p>
                    <p class="text-gray-400">版本：v2.1.0</p>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 飞书分享插件. MIT License.</p>
            </div>
        </div>
    </footer>

    <!-- Enhanced JavaScript -->
    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all elements with fade-in class
        document.addEventListener('DOMContentLoaded', () => {
            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });
        });

        // Counter animation
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16);
            const timer = setInterval(() => {
                start += increment;
                if (start >= target) {
                    element.textContent = target.toLocaleString();
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(start).toLocaleString();
                }
            }, 16);
        }

        // Trigger counter animations when stats section is visible
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counters = entry.target.querySelectorAll('.counter');
                    counters.forEach(counter => {
                        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
                        animateCounter(counter, target);
                    });
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        document.addEventListener('DOMContentLoaded', () => {
            const statsSection = document.querySelector('.stats-section');
            if (statsSection) {
                statsObserver.observe(statsSection);
            }
        });

        // Add floating animation to feature icons
        document.addEventListener('DOMContentLoaded', () => {
            const featureIcons = document.querySelectorAll('.feature-card i');
            featureIcons.forEach((icon, index) => {
                icon.style.animationDelay = `${index * 0.2}s`;
                icon.classList.add('float-animation');
            });
        });

        // Mobile menu toggle (if needed)
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }
    </script>
</body>
</html>
